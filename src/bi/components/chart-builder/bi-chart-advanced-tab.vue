<script setup>
const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
  chartConfig: {
    type: Object,
    required: true,
  },
  formInstance: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['fieldSelected', 'referenceLineConfigChange']);

const colors = ['#101828', '#004EEB', '#D92D20', '#DC6803', '#039855', '#7839EE', '#4CA30D', '#0E9384', '#BA24D5', '#E31B54', '#344054', '#2E90FA', '#F97066', '#FDB022', '#32D583', '#A48AFB', '#66C61C', '#2ED3B7', '#E478FA', '#FD6F8E', '#667085', '#84CAFF', '#FDA29B', '#FEC84B', '#6CE9A6', '#DDD6FE', '#85E13A', '#5FE9D0', '#EEAAFD', '#FEA3B4'];

const state = reactive({
  eighty_percent_line_color: null,
  eighty_percent_line_style: null,
  cumulative_line_color: null,
  bar_color: null,
  positive_color: null,
  negative_color: null,
  sum_color: null,
});

const line_styles = computed(() => {
  return [
    { uid: 'solid', leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 2px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Solid' },
    { uid: 'dashed', leftSlot: IconHawkStroke, tooltip_text: 'Dashed' },
    { uid: 'dotted', leftSlot: IconHawkDotsHorizontal, tooltip_text: 'Dotted' },
  ];
});

function onReferenceLineItemChange(event, index) {
  emit('referenceLineConfigChange', event, index);
}

async function onAddReferenceLine(index) {
  await nextTick();
  emit('referenceLineConfigChange', {
    color: colors[index],
    label: `Reference line ${index + 1}`,
    value: null,
    line_style: 'solid',
  }, index);
}

function onFieldSelected(field, value) {
  state[field] = value;
  props.formInstance.update({
    [field]: value,
  });
}

watch(() => props.chartConfig, () => {
  ['eighty_percent_line_color', 'eighty_percent_line_style', 'cumulative_line_color', 'bar_color', 'positive_color', 'negative_color', 'sum_color'].forEach((key) => {
    state[key] = props.chartConfig[key];
  });
});
</script>

<template>
  <template v-if="['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'].includes(props.chartType)">
    <SelectElement
      name="data_zoom"
      label="Data zoom"
      :items="{
        disabled: 'Disabled',
        slider: 'Slider',
        inside: 'Inside',
        both: 'Both',
      }"
      default="disabled"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Accessibility patterns
      </div>
      <div class="col-span-8">
        <ToggleElement
          name="accessibility_patterns"
          :add-class="{
            text: 'hidden',
          }"
        />
      </div>
    </div>
    <ListElement
      name="reference_lines"
      label="Reference lines"
      add-text="Add reference line"
      :controls="{ add: true, remove: false, sort: true }"
      :add-classes="{
        ListElement: {
          handle: 'left-8 top-[1px] visible opacity-100 !z-0',
          add: '!bg-white !border-white !text-primary-700 hover:!scale-100 !mt-0 -ml-1',
        },
      }"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
        md: { container: 12, label: 12, wrapper: 12 },
      }"
      :initial="0"
      @add="onAddReferenceLine"
    >
      <template #default="{ index }">
        <ObjectElement :name="index">
          <TextElement
            name="label"
            @change="onReferenceLineItemChange({ label: $event }, index)"
          >
            <template #addon-before>
              <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: props.chartConfig?.reference_lines?.[index]?.color }" />
            </template>
            <template #addon-after>
              <div class="flex items-center gap-1">
                <BiReferenceLineContextMenu
                  :reference-line-config="props.chartConfig?.reference_lines?.[index]"
                  @field-selected="onReferenceLineItemChange($event, index)"
                />
                <IconHawkXClose
                  class="w-4 h-4 cursor-pointer"
                  @click="props.formInstance.elements$.reference_lines.remove(index)"
                />
              </div>
            </template>
          </TextElement>
          <HiddenElement name="color" />
          <HiddenElement name="value" />
          <HiddenElement name="line_style" />
        </ObjectElement>
      </template>
    </ListElement>
  </template>
  <template v-if="['pareto_chart'].includes(props.chartType)">
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Show 80% line
      </div>
      <div class="col-span-8">
        <ToggleElement
          name="show_eighty_percent_line"
          :add-class="{
            text: 'hidden',
          }"
        />
      </div>
    </div>
    <div v-if="props.chartConfig.show_eighty_percent_line" class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Line color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.eighty_percent_line_color"
          @color-selected="onFieldSelected('eighty_percent_line_color', $event)"
        />
      </div>
    </div>
    <div v-if="props.chartConfig.show_eighty_percent_line" class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Line style
      </div>
      <div class="col-span-8">
        <HawkButtonGroup
          :items="line_styles"
          icon
          size="sm"
          :active_item="state.eighty_percent_line_style"
          class="w-fit"
          @select="onFieldSelected('eighty_percent_line_style', $event.uid)"
        />
      </div>
    </div>
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Cumulative line color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.cumulative_line_color"
          @color-selected="onFieldSelected('cumulative_line_color', $event)"
        />
      </div>
    </div>
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Bar color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.bar_color"
          @color-selected="onFieldSelected('bar_color', $event)"
        />
      </div>
    </div>
    <HiddenElement name="eighty_percent_line_color" />
    <HiddenElement name="eighty_percent_line_style" />
    <HiddenElement name="cumulative_line_color" />
    <HiddenElement name="bar_color" />
  </template>
  <template v-if="['heatmap_chart'].includes(props.chartType)">
    <BiColorPalettePicker name="color_scheme" :options="{ label: 'Color scheme' }" />
    <SelectElement
      name="color_type"
      label="Color type"
      :items="{
        piecewise: 'Piecewise',
        continuous: 'Continuous',
      }"
      default="continuous"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <ToggleElement
      name="color_scale"
      label="Color scale"
    />
    <SelectElement
      name="color_scale_position"
      label="Color scale position"
      :items="{
        top: 'Top',
        bottom: 'Bottom',
        left: 'Left',
        right: 'Right',
      }"
      :conditions="[['color_scale', '==', true]]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
      default="right"
    />
  </template>
  <template v-if="['waterfall_chart'].includes(props.chartType)">
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Show sum
      </div>
      <div class="col-span-8">
        <ToggleElement
          name="show_sum"
          :add-class="{
            text: 'hidden',
          }"
        />
      </div>
    </div>
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Positive color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.positive_color"
          @color-selected="onFieldSelected('positive_color', $event)"
        />
      </div>
    </div>
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Negative color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.negative_color"
          @color-selected="onFieldSelected('negative_color', $event)"
        />
      </div>
    </div>
    <div v-if="props.chartConfig.show_sum" class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Sum color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="state.sum_color"
          @color-selected="onFieldSelected('sum_color', $event)"
        />
      </div>
    </div>
    <HiddenElement name="positive_color" />
    <HiddenElement name="negative_color" />
    <HiddenElement name="sum_color" />
  </template>
</template>
