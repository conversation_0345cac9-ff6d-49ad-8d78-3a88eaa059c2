<script setup>
import { BI_CHART_COLOR_PALETTES } from '~/bi/constants/bi-constants';

const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  options: {
    type: Object,
    default: () => ({}),
  },
});
</script>

<template>
  <SelectElement
    :name="props.name"
    :items="BI_CHART_COLOR_PALETTES"
    :native="false"
    :can-clear="false"
    :can-deselect="false"
    default="palette0"
    v-bind="props.options"
  >
    <template #option="{ option }">
      <div class="flex items-center gap-2">
        <div class="flex gap-1 p-1" :class="{ 'rounded bg-gray-900': option.label.dark }">
          <div v-for="color in option.label.colors" :key="color" class="w-3 h-3 rounded-full" :style="{ backgroundColor: color }" />
        </div>
      </div>
    </template>
    <template #single-label="{ value }">
      <div class="w-full flex items-center gap-2 px-2">
        <div class="flex gap-1 p-1" :class="{ 'rounded bg-gray-900': value.label.dark }">
          <div v-for="color in value.label.colors" :key="color" class="w-3 h-3 rounded-full" :style="{ backgroundColor: color }" />
        </div>
      </div>
    </template>
  </SelectElement>
</template>
