<script setup>
const props = defineProps({
  seriesConfig: {
    type: Object,
    required: true,
  },
  chartConfig: {
    type: Object,
    required: true,
  },
  columns: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(['fieldSelected']);

const state = reactive({
  is_menu_open: false,
  value: null,
  chart_type: null,
  color: null,
  y_axis: null,
  line_style: null,
  line_width: null,
  line_shape: null,
  legend: null,
  prefix: null,
  suffix: null,
  stack: null,
});

const chart_types = computed(() => {
  return [
    { uid: 'bar', leftSlot: h(IconHawkHorizontalBarChartOne, { style: 'transform: rotate(-90deg);' }), tooltip_text: 'Bar' },
    { uid: 'line', leftSlot: IconHawkLineChartUpOne, tooltip_text: 'Line' },
    { uid: 'area', leftSlot: IconHawkAreaChart, tooltip_text: 'Area' },
  ];
});

const y_axis_options = computed(() => [
  { uid: 'primary', label: 'Primary' },
  { uid: 'secondary', label: 'Secondary' },
]);

const line_styles = computed(() => {
  return [
    { uid: 'solid', leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 2px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Solid' },
    { uid: 'dashed', leftSlot: IconHawkStroke, tooltip_text: 'Dashed' },
    { uid: 'dotted', leftSlot: IconHawkDotsHorizontal, tooltip_text: 'Dotted' },
  ];
});

const line_widths = computed(() => {
  return [
    { uid: 1, leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 1px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Thin' },
    { uid: 2, leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 2px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Medium' },
    { uid: 3, leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 3px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Thick' },
  ];
});

const line_shapes = computed(() => {
  return [
    { uid: 'straight', leftSlot: IconHawkSquareLine, tooltip_text: 'Straight' },
    { uid: 'curved', leftSlot: IconHawkSmoothLine, tooltip_text: 'Curved' },
  ];
});

function onFieldSelected(field_name, value) {
  state[field_name] = value;
  emit('fieldSelected', { [field_name]: value });
}

function onMenuOpen() {
  Object.keys(props.seriesConfig).forEach((key) => {
    state[key] = props.seriesConfig[key];
  });
  state.is_menu_open = true;
}
</script>

<template>
  <div class="flex items-center gap-1">
    <HawkMenu
      position="fixed"
      additional_trigger_classes="mt-1.5 p-0 m-0 !ring-0 !border-0 focus:!ring-0"
      additional_dropdown_classes="ml-4"
      @open="onMenuOpen"
      @close="state.is_menu_open = false"
    >
      <template #trigger>
        <IconHawkDotsVertical class="w-4 h-4" />
      </template>
      <template #content>
        <div class="px-3.5 py-3">
          <div class="mb-3">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">
              Configure series
            </h3>
            <hr class="-mx-3.5">
          </div>

          <Vueform
            v-if="state.is_menu_open"
            size="sm"
            :columns="{
              default: { container: 12, label: 4, wrapper: 12 },
              sm: { container: 12, label: 4, wrapper: 12 },
              md: { container: 12, label: 4, wrapper: 12 },
            }"
            :add-classes="{
              FormElements: {
                container: '!gap-y-4',
              },
            }"
          >
            <div class="col-span-12">
              <div class="flex items-center gap-3">
                <BiColorPicker :active-color="state.color" @color-selected="onFieldSelected('color', $event)" />
                <div class="flex-1">
                  <SelectElement
                    name="value"
                    :items="columns"
                    :native="false"
                    :can-clear="false"
                    :can-deselect="false"
                    :default="state.value"
                    placeholder="Select series"
                    :add-classes="{
                      SelectElement: {
                        select: {
                          container: '!min-h-7 !h-7',
                        },
                      },
                    }"
                    @change="onFieldSelected('value', $event)"
                  />
                </div>
              </div>
            </div>

            <TextElement
              name="legend"
              label="Name"
              :default="state.legend"
              placeholder="Enter legend text"
              :add-classes="{
                TextElement: {
                  inputContainer: '!h-7',
                },
              }"
              @change="onFieldSelected('legend', $event)"
            />

            <div v-show="props.chartConfig?.chart_type !== 'scatter_chart'" class="col-span-12">
              <div class="grid grid-cols-12 gap-0 items-center">
                <label class="col-span-4 text-sm font-medium text-gray-700">Chart type</label>
                <div class="col-span-8">
                  <HawkButtonGroup
                    :items="chart_types"
                    icon
                    size="sm"
                    :active_item="state.chart_type"
                    class="w-fit"
                    @select="onFieldSelected('chart_type', $event.uid)"
                  />
                </div>
              </div>
            </div>

            <div class="col-span-12">
              <div class="grid grid-cols-12 gap-0 items-center">
                <label class="col-span-4 text-sm font-medium text-gray-700">Y-axis</label>
                <div class="col-span-8">
                  <HawkButtonGroup
                    :items="y_axis_options"
                    size="sm"
                    :active_item="state.y_axis"
                    class="w-fit"
                    @select="onFieldSelected('y_axis', $event.uid)"
                  />
                </div>
              </div>
            </div>

            <div v-show="props.chartConfig?.chart_type !== 'scatter_chart'" class="col-span-12">
              <div class="grid grid-cols-12 gap-0 items-center">
                <label class="col-span-4 text-sm font-medium text-gray-700">Line style</label>
                <div class="col-span-8">
                  <HawkButtonGroup
                    :items="line_styles"
                    icon
                    size="sm"
                    :active_item="state.line_style"
                    class="w-fit"
                    @select="onFieldSelected('line_style', $event.uid)"
                  />
                </div>
              </div>
            </div>

            <div v-show="props.chartConfig?.chart_type !== 'scatter_chart'" class="col-span-12">
              <div class="grid grid-cols-12 gap-0 items-center">
                <label class="col-span-4 text-sm font-medium text-gray-700">Line width</label>
                <div class="col-span-8">
                  <HawkButtonGroup
                    :items="line_widths"
                    icon
                    size="sm"
                    :active_item="state.line_width"
                    class="w-fit"
                    @select="onFieldSelected('line_width', $event.uid)"
                  />
                </div>
              </div>
            </div>

            <div v-show="props.chartConfig?.chart_type !== 'scatter_chart'" class="col-span-12">
              <div class="grid grid-cols-12 gap-0 items-center">
                <label class="col-span-4 text-sm font-medium text-gray-700">Line shape</label>
                <div class="col-span-8">
                  <HawkButtonGroup
                    :items="line_shapes"
                    icon
                    size="sm"
                    :active_item="state.line_shape"
                    class="w-fit"
                    @select="onFieldSelected('line_shape', $event.uid)"
                  />
                </div>
              </div>
            </div>

            <TextElement
              name="prefix"
              label="Prefix"
              :default="state.prefix"
              placeholder="Enter prefix"
              :add-classes="{
                TextElement: {
                  inputContainer: '!h-7',
                },
              }"
              @change="onFieldSelected('prefix', $event)"
            />

            <TextElement
              name="suffix"
              label="Suffix"
              :default="state.suffix"
              placeholder="Enter suffix"
              :add-classes="{
                TextElement: {
                  inputContainer: '!h-7',
                },
              }"
              @change="onFieldSelected('suffix', $event)"
            />

            <CheckboxElement
              v-if="props.chartConfig.stack_by && props.chartConfig.stack_by !== 'none'"
              name="stack"
              :default="state.stack"
              @change="onFieldSelected('stack', $event)"
            >
              <template #label>
                <div class="flex items-center gap-1">
                  <label class="text-sm font-medium text-gray-700">Stack</label>
                  <IconHawkInfoCircle class="w-4 h-4 text-gray-400" />
                </div>
              </template>
            </CheckboxElement>
          </Vueform>
        </div>
      </template>
    </HawkMenu>
  </div>
</template>
