<script setup>
const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
  chartConfig: {
    type: Object,
    required: true,
  },
});

const state = reactive({
  are_more_options_visible: false,
});

const scale_types = [
  ['linear', 'Linear'],
  ['log', 'Logarithmic'],
].map((item) => {
  return {
    value: item[0],
    label: item[1],
  };
});

const tick_label_types = [
  ['show', 'Show'],
  ['hide', 'Hide'],
  ['compact', 'Compact'],
  ['rotate45', 'Rotate 45°'],
  ['rotate90', 'Rotate 90°'],
].map((item) => {
  return {
    value: item[0],
    label: item[1],
  };
});

const has_axes_names = computed(() => ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'waterfall_chart', 'scatter_chart'].includes(props.chartType));
const has_dual_y_axis = computed(() => ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'].includes(props.chartType));
const has_scale_type = computed(() => ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'].includes(props.chartType));
const has_tick_labels = computed(() => ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'waterfall_chart'].includes(props.chartType));
const has_primary_custom_range = computed(() => ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart', 'waterfall_chart'].includes(props.chartType));
const has_secondary_custom_range = computed(() => ['scatter_chart'].includes(props.chartType));

function getAxis(type) {
  // type = 'category' || 'value'
  if (props.chartConfig.chart_type === 'horizontalBar_chart') {
    if (type === 'category')
      return 'y';
    else return 'x';
  }
  else {
    if (type === 'category')
      return 'x';
    else return 'y';
  }
}
</script>

<template>
  <template v-if="has_axes_names">
    <div class="text-sm font-semibold text-gray-900 -mb-3">
      Axis names
    </div>
    <div class="flex gap-3">
      <TextElement
        name="category_axis_name"
        :label="getAxis('category') === 'x' ? 'X-axis' : 'Y-axis'"
        placeholder="Enter label"
      />
      <TextElement
        name="value_axis_name"
        :label="getAxis('value') === 'x' ? 'X-axis' : 'Y-axis'"
        placeholder="Enter label"
      />
    </div>
  </template>
  <template v-if="has_dual_y_axis">
    <!-- <div v-if="getAxis('value') === 'y'" class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Dual Y-axis
      </div>
      <div class="col-span-8">
        <ToggleElement
          name="dual_y_axis"
          :add-class="{
            text: 'hidden',
          }"
        />
      </div>
    </div>
    :conditions="[['dual_y_axis', '==', true]]"
    -->
    <TextElement
      v-if="getAxis('value') === 'y'"
      name="secondary_y_axis"
      label="Secondary Y-axis name"
      placeholder="Enter label"
      class="-mt-3"
    />
  </template>
  <div class="w-40 cursor-pointer" @click="state.are_more_options_visible = !state.are_more_options_visible">
    <div class="flex items-center text-sm font-semibold text-primary-700">
      <IconHawkChevronRight v-if="!state.are_more_options_visible" class="w-6 h-6" />
      <IconHawkChevronDown v-else class="w-6 h-6" />
      More options
    </div>
  </div>
  <div v-show="state.are_more_options_visible" class="col-span-12 flex flex-col gap-y-5 -mt-3">
    <template v-if="has_scale_type">
      <div class="text-sm font-semibold text-gray-900 -mb-3">
        Scale type
      </div>
      <div class="flex gap-3">
        <SelectElement
          name="primary_scale"
          label="Primary"
          :items="scale_types"
          :native="false"
          :can-clear="false"
          :can-deselect="false"
          default="linear"
          class="w-full"
        />
        <SelectElement
          name="secondary_scale"
          label="Secondary"
          :items="scale_types"
          :native="false"
          :can-clear="false"
          :can-deselect="false"
          default="linear"
          class="w-full"
        />
      </div>
    </template>
    <template v-if="has_tick_labels">
      <div class="text-sm font-semibold text-gray-900 -mb-3">
        Tick labels
      </div>
      <div class="flex gap-3">
        <SelectElement
          name="category_tick_label"
          :label="getAxis('category') === 'x' ? 'X-axis' : 'Y-axis'"
          :items="tick_label_types"
          :native="false"
          :can-clear="false"
          :can-deselect="false"
          default="show"
          class="w-full"
        />
        <SelectElement
          name="value_tick_label"
          :label="getAxis('value') === 'x' ? 'X-axis' : 'Y-axis'"
          :items="tick_label_types"
          :native="false"
          :can-clear="false"
          :can-deselect="false"
          default="show"
          class="w-full"
        />
      </div>
      <SelectElement
        name="secondary_value_tick_label"
        label="Secondary Y-axis"
        :items="tick_label_types"
        :conditions="[['dual_y_axis', '==', true]]"
        :native="false"
        :can-clear="false"
        :can-deselect="false"
        default="show"
        class="w-1/2 -mt-3"
      />
    </template>
    <template v-if="has_primary_custom_range">
      <div class="text-sm font-semibold text-gray-900 -mb-3">
        Custom range
      </div>
      <div class="flex gap-3">
        <TextElement
          name="custom_range_min"
          label="Minimum"
          input-type="number"
        />
        <TextElement
          name="custom_range_max"
          label="Maximum"
          input-type="number"
        />
      </div>
    </template>
    <template v-if="has_secondary_custom_range">
      <div class="text-sm font-semibold text-gray-900 -mb-3">
        Secondary custom range
      </div>
      <div class="flex gap-3">
        <TextElement
          name="secondary_value_min"
          label="Minimum"
          input-type="number"
        />
        <TextElement
          name="secondary_value_max"
          label="Maximum"
          input-type="number"
        />
      </div>
    </template>
  </div>
</template>
