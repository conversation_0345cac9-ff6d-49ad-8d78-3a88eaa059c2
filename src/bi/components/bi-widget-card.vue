<script setup>
import { generateChartConfig } from '@sensehawk/chart-generator';
import * as echarts from 'echarts';
import { nextTick } from 'vue';
import { sleep } from '~/common/utils/common.utils';

const props = defineProps({
  widget: {
    type: Object,
    required: true,
  },
});

const data_one = [
  {
    department: 'Engineering',
    budget: 500000,
  },
  {
    department: 'Engineering',
    budget: 200000,
  },
  {
    department: 'Marketing',
    budget: 300000,
  },
  {
    department: 'Marketing',
    budget: 150000,
  },
  {
    department: 'Sales',
    budget: 250000,
  },
  {
    department: 'HR',
    budget: 180000,
  },
  {
    department: 'HR',
    budget: 70000,
  },
  {
    department: 'Operations',
    budget: 220000,
  },
];

const config_one = {
  type: 'pie',
  data: {
    category: 'department',
    values: [
      'budget',
    ],
  },
  layout: {
    title: 'Department Budget Distribution',
    subtitle: 'Duplicate entries automatically aggregated',
  },
  processing: {
    aggregation: 'sum',
  },
  dataValues: {
    show: true,
    prefix: '$',
    compact: true,
    precision: 1,
  },
  legend: {
    show: true,
    position: 'right',
  },
};

const data_two = [
  {
    Category: 'Design',
    Quarter: 'Q1',
    Planned: '10',
    Actual: 8,
  },
  {
    Category: 'Design',
    Quarter: 'Q1',
    Planned: '10',
    Actual: 10,
  },
  {
    Category: 'Design',
    Quarter: 'Q2',
    Planned: '25',
    Actual: 22,
  },
  {
    Category: 'Engineering',
    Quarter: 'Q1',
    Planned: '15',
    Actual: 14,
  },
  {
    Category: 'Engineering',
    Quarter: 'Q2',
    Planned: '18',
    Actual: 16,
  },
  {
    Category: 'Design and Engineering',
    Quarter: 'Q2',
    Planned: '18',
    Actual: 30,
  },
];

const config_two = {
  type: 'horizontalBar',
  data: {
    category: 'Quarter',
    values: [
      'Planned',
    ],
    stackBy: null,
  },
  series: {
    Planned: {
      name: 'Planned',
      type: 'bar',
      yAxisIndex: 0,
      color: '#101828',
      lineColor: '#101828',
      lineWidth: 1,
      lineStyle: 'solid',
      smooth: false,
      prefix: '',
      suffix: '',
    },
  },
  layout: {
    title: null,
    subtitle: null,
  },
  legend: {
    show: true,
    position: 'bottom',
  },
  dataValues: {
    show: false,
    compact: false,
    precision: null,
  },
  styling: {
    colors: [
      '#77BEF0',
      '#FFCB61',
      '#FF894F',
      '#EA5B6F',
      '#9B59B6',
      '#2ECC71',
      '#F39C12',
    ],
  },
  processing: {
    aggregation: 'sum',
  },
  axes: {
    categoryName: null,
    valueName: null,
    categoryLabels: 'show',
    valueLabels: 'show',
    secondaryValueLabels: 'show',
    valueScale: 'linear',
    secondaryValueScale: 'linear',
  },
  accessibility: {
    enabled: false,
    decalPatterns: false,
  },
  interactions: {
    dataZoom: {
      enabled: false,
      type: 'disabled',
    },
  },
};

const state = reactive({
  chart_instance: null,
  resize_observer: null,
});

onMounted(async () => {
  // await nextTick();
  // await sleep(1000);
  // const chartDom = document.getElementById(props.widget.widget_id);
  // const myChart = echarts.init(chartDom);
  // let option;

  // option = {
  //   series: [
  //     {
  //       name: 'value',
  //       fieldName: 'value',
  //       type: 'bar',
  //       data: [
  //         100,
  //         200,
  //       ],
  //       yAxisIndex: 0,
  //       itemStyle: {
  //         color: '#1f77b4',
  //         borderColor: '#1f77b4',
  //         borderWidth: 2,
  //         borderType: 'solid',
  //       },
  //       label: {
  //         show: true,
  //         position: 'top',
  //       },
  //       emphasis: {
  //         focus: 'series',
  //         blurScope: 'coordinateSystem',
  //       },
  //     },
  //   ],
  //   xAxis: {
  //     type: 'category',
  //     name: '',
  //     nameLocation: 'middle',
  //     axisLabel: {
  //       show: true,
  //       rotate: 0,
  //     },
  //     axisLine: {
  //       show: true,
  //     },
  //     axisTick: {
  //       show: true,
  //     },
  //     data: [
  //       'A',
  //       'B',
  //     ],
  //   },
  //   yAxis: {
  //     type: 'value',
  //     name: '',
  //     nameLocation: 'middle',
  //     axisLabel: {
  //       show: true,
  //     },
  //     axisLine: {
  //       show: true,
  //     },
  //     axisTick: {
  //       show: true,
  //     },
  //     splitLine: {
  //       show: true,
  //     },
  //   },
  //   title: {
  //     text: 'Sample Chart',
  //     subtext: '',
  //     left: 'center',
  //     textStyle: {
  //       fontSize: 16,
  //       fontWeight: 'bold',
  //     },
  //     subtextStyle: {
  //       fontSize: 12,
  //       color: '#666',
  //     },
  //   },
  //   legend: {
  //     show: true,
  //     type: 'scroll',
  //   },
  //   animation: true,
  //   animationDuration: 300,
  //   animationEasing: 'cubicOut',
  //   tooltip: {
  //     trigger: 'axis',
  //     axisPointer: {
  //       type: 'shadow',
  //     },
  //     backgroundColor: 'rgba(255, 255, 255, 0.9)',
  //     borderColor: '#ccc',
  //     borderWidth: 1,
  //     textStyle: {
  //       color: '#333',
  //     },
  //   },
  // };

  // option && myChart.setOption(option);
  // state.resize_observer = new ResizeObserver(() => {
  //   console.log('resize');
  //   state.chart_instance?.resize();
  // });

  // state.resize_observer.observe(document.getElementById(props.widget.widget_id));
  // return;
  if (!props.widget.widget_id)
    return;
  await nextTick();
  await sleep(1000);
  let echarts_config;
  if (props.widget.widget_id === 'first_widget')
    echarts_config = generateChartConfig(data_one, config_one);
  else if (props.widget.widget_id === 'second_widget')
    echarts_config = generateChartConfig(data_two, config_two);
  const chartElement = document.getElementById(props.widget.widget_id);

  if (!state.chart_instance) {
    state.chart_instance = echarts.init(chartElement);
  }
  else if (state.chart_instance) {
    state.chart_instance.dispose();
    state.chart_instance = echarts.init(chartElement);
  }

  state.chart_instance.setOption({
    series: [
      {
        name: 'value',
        fieldName: 'value',
        type: 'bar',
        data: [
          100,
          200,
        ],
        yAxisIndex: 0,
        itemStyle: {
          color: '#1f77b4',
          borderColor: '#1f77b4',
          borderWidth: 2,
          borderType: 'solid',
        },
        label: {
          show: true,
          position: 'top',
        },
        emphasis: {
          focus: 'series',
          blurScope: 'coordinateSystem',
        },
      },
    ],
    xAxis: {
      type: 'category',
      name: '',
      nameLocation: 'middle',
      axisLabel: {
        show: true,
        rotate: 0,
      },
      axisLine: {
        show: true,
      },
      axisTick: {
        show: true,
      },
      data: [
        'A',
        'B',
      ],
    },
    yAxis: {
      type: 'value',
      name: '',
      nameLocation: 'middle',
      axisLabel: {
        show: true,
      },
      axisLine: {
        show: true,
      },
      axisTick: {
        show: true,
      },
      splitLine: {
        show: true,
      },
    },
    title: {
      text: 'Sample Chart',
      subtext: '',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
      subtextStyle: {
        fontSize: 12,
        color: '#666',
      },
    },
    legend: {
      show: true,
      type: 'scroll',
    },
    animation: true,
    animationDuration: 300,
    animationEasing: 'cubicOut',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333',
      },
    },
  });

  state.resize_observer = new ResizeObserver(() => {
    state.chart_instance?.resize();
  });

  state.resize_observer.observe(chartElement);
});

onUnmounted(() => {
  const chartElement = document.getElementById(props.widget.widget_id);
  if (chartElement) {
    state.resize_observer?.unobserve(chartElement);
  }
  state.chart_instance?.dispose();
});
</script>

<template>
  <div class="w-full h-full">
    <div :id="props.widget.widget_id" class="w-full h-full" />
  </div>
</template>
