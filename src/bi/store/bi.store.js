import { acceptHMRUpdate, defineStore } from 'pinia';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';

const bi_query_builder = useBIQueryBuilder();
const all_tables = [
  {
    label: 'Progress History',
    columns: [{ label: 'ID', type: 'id' }, { label: 'Activity', type: 'text' }, { label: 'Subactivity', type: 'text' }, { label: 'Layer', type: 'text' }, { label: 'Sublayer', type: 'text' }, { label: 'Date', type: 'date' }, { label: 'Scope', type: 'integer' }, { label: 'Work Done', type: 'integer' }, { label: 'Temperature', type: 'float' }, { label: 'Status', type: 'text' }, { label: 'Created At', type: 'timestamp' }, { label: 'Is Active', type: 'boolean' }],
  },
  {
    label: 'Site Progress History',
    columns: [
      { label: 'Site ID', type: 'numeric' },
      { label: 'Activity', type: 'text' },
      { label: 'Completion %', type: 'numeric' },
      { label: 'Date', type: 'date' },
    ],
  },
  {
    label: 'Material Delivery Log',
    columns: [
      { label: 'Delivery ID', type: 'numeric' },
      { label: 'Material', type: 'text' },
      { label: 'Quantity', type: 'numeric' },
      { label: 'Delivery Date', type: 'date' },
      { label: 'Status', type: 'text' },
    ],
  },
  {
    label: 'Workforce Deployment',
    columns: [
      { label: 'Team ID', type: 'numeric' },
      { label: 'Crew Leader', type: 'text' },
      { label: 'No. of Workers', type: 'numeric' },
      { label: 'Assigned Task', type: 'text' },
      { label: 'Shift Date', type: 'date' },
    ],
  },
  {
    label: 'Inspection Records',
    columns: [
      { label: 'Inspection ID', type: 'numeric' },
      { label: 'Component', type: 'text' },
      { label: 'Status', type: 'text' },
      { label: 'Inspector', type: 'text' },
      { label: 'Inspection Date', type: 'date' },
    ],
  },
].map(table => ({ ...table, columns: table.columns.map(column => ({ ...column, key: bi_query_builder.constructColumnKeys(table, column) })) }));

export const useBiStore = defineStore('bi', {
  state: () => ({
    all_tables,
    selected_table: all_tables[0],
    stages: [],
    selected_stage_index: null,
    are_chart_builder_fields_filled: false,
    chart_builder_config: {},
    chart_builder_data: [
      { Category: 'Design', Quarter: 'Q1', Planned: '10', Actual: 8 },
      { Category: 'Design', Quarter: 'Q1', Planned: '10', Actual: 10 },
      { Category: 'Design', Quarter: 'Q2', Planned: '25', Actual: 22 },
      { Category: 'Engineering', Quarter: 'Q1', Planned: '15', Actual: 14 },
      { Category: 'Engineering', Quarter: 'Q2', Planned: '18', Actual: 16 },
      { Category: 'Design and Engineering', Quarter: 'Q2', Planned: '18', Actual: 30 },
    ],
  }),
  getters: {
    get_chart_builder_data_types() {
      if (!this.chart_builder_data || this.chart_builder_data.length === 0) {
        return {};
      }

      const types = {};
      const sample_size = Math.max(1, Math.ceil(this.chart_builder_data.length * 0.05));

      const first_row = this.chart_builder_data[0];
      const column_names = Object.keys(first_row);

      const column_type_counts = {};
      column_names.forEach((column) => {
        column_type_counts[column] = {
          date: 0,
          numeric: 0,
          text: 0,
          null_count: 0,
          total_checked: 0,
        };
      });

      for (let i = 0; i < sample_size && i < this.chart_builder_data.length; i++) {
        const row = this.chart_builder_data[i];

        for (const column of column_names) {
          const value = row[column];
          column_type_counts[column].total_checked++;

          if (value === null || value === undefined) {
            column_type_counts[column].null_count++;
            continue;
          }

          if (value instanceof Date) {
            column_type_counts[column].date++;
          }
          else if (typeof value === 'number' || value instanceof Number || !Number.isNaN(Number.parseFloat(value))) {
            column_type_counts[column].numeric++;
          }
          else if (typeof value === 'string') {
            column_type_counts[column].text++;
          }
        }
      }

      for (const column of column_names) {
        const counts = column_type_counts[column];
        const non_null_count = counts.total_checked - counts.null_count;

        if (non_null_count === 0) {
          types[column] = 'mixed';
          continue;
        }

        const type_percentages = {
          date: counts.date / non_null_count,
          numeric: counts.numeric / non_null_count,
          text: counts.text / non_null_count,
        };

        const dominant_type = Object.keys(type_percentages).reduce((a, b) =>
          type_percentages[a] > type_percentages[b] ? a : b,
        );

        // If the dominant type represents 100% of non-null values, it is the type of that column
        if (type_percentages[dominant_type] === 1.0) {
          types[column] = dominant_type;
        }
        else {
          types[column] = 'mixed';
        }
      }

      return types;
    },
  },
  actions: {
  },
});

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useBiStore, import.meta.hot));
